const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('IngredientUsage', {
    UsageID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    ProductCartID: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'ProductCart',
        key: 'ProductCartID'
      }
    },
    IngredientID: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Ingredient',
        key: 'IngredientID'
      }
    },
    IsNeeded: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: true
    },
    Quantity: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1
    }
  }, {
    sequelize,
    tableName: 'IngredientUsage',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Ingredie__29B197C0A57A6106",
        unique: true,
        fields: [
          { name: "Usage<PERSON>" },
        ]
      },
    ]
  });
};
