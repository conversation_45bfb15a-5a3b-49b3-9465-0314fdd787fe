import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:otlop_app/models/product_model.dart';
import 'package:uuid/uuid.dart';

class CartStorageService {
  // Keys for SharedPreferences
  static const String _cartItemsKey = 'cart_items';

  // Singleton instance
  static CartStorageService? _instance;

  // SharedPreferences instance
  late SharedPreferences _prefs;

  // Private constructor
  CartStorageService._();

  // Factory constructor to return the same instance
  factory CartStorageService() {
    _instance ??= CartStorageService._();
    return _instance!;
  }

  // Initialize the service
  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Get all cart items
  Future<List<CartItem>> getCartItems() async {
    final String? cartItemsJson = _prefs.getString(_cartItemsKey);

    if (cartItemsJson == null || cartItemsJson.isEmpty) {
      debugPrint('DEBUG: No cart items found in SharedPreferences');
      return [];
    }

    try {
      debugPrint('DEBUG: Cart items JSON: $cartItemsJson');
      final List<dynamic> decodedList = jsonDecode(cartItemsJson);
      debugPrint('DEBUG: Decoded JSON list: $decodedList');
      final items = decodedList.map((item) => CartItem.fromJson(item)).toList();
      debugPrint('DEBUG: Parsed ${items.length} cart items');
      debugPrint('DEBUG: Cart items data: ${items.length} items');
      debugPrint('DEBUG: THE RESTAAAAAURANT ID FIRST IS >>>: ${items.first.restaurantName} items');
      for (var item in items) {
        debugPrint('DEBUG: Item ID: ${item.id}, Product ID: ${item.productId}, Quantity: ${item.quantity}');
      }
      return items;
    } catch (e) {
      debugPrint('ERROR: Error parsing cart items: $e');
      return [];
    }
  }

  // Save cart items
  Future<void> saveCartItems(List<CartItem> cartItems) async {
    debugPrint('DEBUG: Saving ${cartItems.length} cart items');
    final List<Map<String, dynamic>> itemsJson = cartItems.map((item) => item.toJson()).toList();
    debugPrint('DEBUG: Items JSON: $itemsJson');
    final String encodedList = jsonEncode(itemsJson);
    debugPrint('DEBUG: Encoded JSON: $encodedList');
    await _prefs.setString(_cartItemsKey, encodedList);

    // Verify the save worked
    final String? savedJson = _prefs.getString(_cartItemsKey);
    debugPrint('DEBUG: Saved JSON: $savedJson');
  }

  // Add item to cart
  Future<void> addToCart(Product product, int quantity, List<SelectedIngredient>? selectedIngredients) async {
    debugPrint('DEBUG: Adding product to cart: ${product.id} - ${product.name}');
    debugPrint('DEBUG: Product details: price=${product.price}, image=${product.image}, restaurantId=${product.restaurantId}');

    final List<CartItem> currentCart = await getCartItems();
    debugPrint('DEBUG: Current cart has ${currentCart.length} items');

    // Check if item from same restaurant
    if (currentCart.isNotEmpty) {
      final cartRestaurantId = currentCart.first.restaurantId;
      final cartRestaurantName = currentCart.first.restaurantName;
      final productRestaurantId = product.restaurantId ?? 0;
      final productRestaurantName = product.restaurantName ?? 'Unknown Restaurant';

      debugPrint('DEBUG: Comparing restaurant IDs: cart=$cartRestaurantId ($cartRestaurantName), product=$productRestaurantId ($productRestaurantName)');
      debugPrint('DEBUG: Restaurant ID types: cart=${cartRestaurantId.runtimeType}, product=${productRestaurantId.runtimeType}');

      if (cartRestaurantId != productRestaurantId) {
        debugPrint('DEBUG: Restaurant ID mismatch: cart=$cartRestaurantId ($cartRestaurantName), product=$productRestaurantId ($productRestaurantName)');
        throw Exception('Cannot add items from different restaurants. You already have items from $cartRestaurantName in your cart.');
      }
    }

    // Generate a unique cart item ID
    const uuid = Uuid();
    final cartItemId = int.parse(uuid.v4().substring(0, 8), radix: 16);
    debugPrint('DEBUG: Generated cart item ID: $cartItemId');

    // Validate restaurant ID
    if (product.restaurantId == null || product.restaurantId == 0) {
      debugPrint('ERROR: Product ${product.name} has invalid restaurant ID: ${product.restaurantId}');
      throw Exception('Product does not have a valid restaurant ID. Cannot add to cart.');
    }

    // Create new cart item
    final cartItem = CartItem(
      id: cartItemId,
      productId: product.id,
      productName: product.name,
      productImage: product.image,
      price: product.price,
      quantity: quantity,
      restaurantId: product.restaurantId!,
      restaurantName: product.restaurantName ?? 'Restaurant',
      selectedIngredients: selectedIngredients,
    );

    debugPrint('DEBUG: Created cart item: id=${cartItem.id}, name=${cartItem.productName}, price=${cartItem.price}');

    // Add to cart
    currentCart.add(cartItem);
    debugPrint('DEBUG: Added item to cart. New cart size: ${currentCart.length}');
    await saveCartItems(currentCart);
  }

  // Update cart item quantity
  Future<void> updateCartItemQuantity(int cartItemId, int quantity) async {
    final List<CartItem> currentCart = await getCartItems();

    final int index = currentCart.indexWhere((item) => item.id == cartItemId);
    if (index == -1) {
      throw Exception('Cart item not found');
    }

    // Create updated item
    final CartItem oldItem = currentCart[index];
    final CartItem updatedItem = CartItem(
      id: oldItem.id,
      productId: oldItem.productId,
      productName: oldItem.productName,
      productImage: oldItem.productImage,
      price: oldItem.price,
      quantity: quantity,
      restaurantId: oldItem.restaurantId,
      restaurantName: oldItem.restaurantName,
      selectedIngredients: oldItem.selectedIngredients,
    );

    // Replace item in cart
    currentCart[index] = updatedItem;
    await saveCartItems(currentCart);
  }

  // Remove item from cart
  Future<void> removeCartItem(int cartItemId) async {
    final List<CartItem> currentCart = await getCartItems();

    final List<CartItem> updatedCart = currentCart.where((item) => item.id != cartItemId).toList();
    await saveCartItems(updatedCart);
  }

  // Clear cart
  Future<void> clearCart() async {
    await _prefs.remove(_cartItemsKey);
  }

  // Calculate cart total
  Future<double> getCartTotal() async {
    final List<CartItem> cartItems = await getCartItems();
    double currentTotal = 0.0;
    for (final item in cartItems) {
      // Simply multiply price by quantity
      currentTotal += item.price * item.quantity;
    }
    debugPrint('DEBUG: Cart total: $currentTotal');
    return currentTotal;
  }

  // Check if a product is already in the cart
  Future<bool> isProductInCart(int productId) async {
    final List<CartItem> cartItems = await getCartItems();
    return cartItems.any((item) => item.productId == productId);
  }

  // Check if a product is from a different restaurant than what's in the cart
  Future<bool> isFromDifferentRestaurant(int restaurantId) async {
    final List<CartItem> cartItems = await getCartItems();

    // If cart is empty, product can be added
    if (cartItems.isEmpty) {
      debugPrint('DEBUG: Cart is empty, can add product from any restaurant');
      return false;
    }

    // Check if restaurant ID matches the first item in cart
    final cartRestaurantId = cartItems.first.restaurantId;
    final cartRestaurantName = cartItems.first.restaurantName;

    debugPrint('DEBUG: Checking if product is from different restaurant');
    debugPrint('DEBUG: Cart restaurant: ID=$cartRestaurantId, Name=$cartRestaurantName');
    debugPrint('DEBUG: Product restaurant ID=$restaurantId');
    debugPrint('DEBUG: Types - cart=${cartRestaurantId.runtimeType}, product=${restaurantId.runtimeType}');

    final isDifferent = cartRestaurantId != restaurantId;
    debugPrint('DEBUG: Is from different restaurant? $isDifferent');

    return isDifferent;
  }

  // Get the current restaurant in cart (if any)
  Future<Map<String, dynamic>?> getCurrentRestaurantInCart() async {
    final List<CartItem> cartItems = await getCartItems();

    if (cartItems.isEmpty) {
      return null;
    }

    return {
      'id': cartItems.first.restaurantId,
      'name': cartItems.first.restaurantName,
    };
  }
}
