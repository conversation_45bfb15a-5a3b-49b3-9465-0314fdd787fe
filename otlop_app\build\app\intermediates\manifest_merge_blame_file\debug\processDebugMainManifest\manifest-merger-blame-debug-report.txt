1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.otlop_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:39:5-44:15
18        <intent>
18-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:41:13-72
19-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:42:13-50
21-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:42:19-48
22        </intent>
23        <!-- Needs to be explicitly declared on Android R+ -->
24        <package android:name="com.google.android.apps.maps" />
24-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
24-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
25    </queries>
26    <!-- Permissions for location access -->
27    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
27-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:46:5-79
27-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:46:22-76
28    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
28-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:47:5-81
28-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:47:22-78
29    <uses-permission android:name="android.permission.INTERNET" />
29-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-67
29-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-64
30    <uses-permission android:name="android.permission.WAKE_LOCK" />
30-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
30-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- Permissions options for the `notification` group -->
31-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
31-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
32    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
32-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-77
32-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:22-74
33    <uses-permission android:name="android.permission.VIBRATE" /> <!-- Required by older versions of Google Play services to create IID tokens -->
33-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
33-->[:flutter_local_notifications] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\flutter_local_notifications\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
34    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
34-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:5-82
34-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:26:22-79
35
36    <uses-feature
36-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
37        android:glEsVersion="0x00020000"
37-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
38        android:required="true" />
38-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
39
40    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
40-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e260382871c528da7b61d84dd06ad26\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:5-98
40-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4e260382871c528da7b61d84dd06ad26\transformed\jetified-recaptcha-18.4.0\AndroidManifest.xml:9:22-95
41
42    <permission
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
43        android:name="com.example.otlop_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
44        android:protectionLevel="signature" />
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
45
46    <uses-permission android:name="com.example.otlop_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
47
48    <application
49        android:name="android.app.Application"
49-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:4:9-42
50        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\dfc2fbca530748e569b0737b09fa016f\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
51        android:debuggable="true"
52        android:extractNativeLibs="true"
53        android:icon="@mipmap/ic_launcher"
53-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:5:9-43
54        android:label="otlop_app" >
54-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:3:9-34
55        <activity
55-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:6:9-27:20
56            android:name="com.example.otlop_app.MainActivity"
56-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:7:13-41
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:12:13-163
58            android:exported="true"
58-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:8:13-36
59            android:hardwareAccelerated="true"
59-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:13:13-47
60            android:launchMode="singleTop"
60-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:9:13-43
61            android:taskAffinity=""
61-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:10:13-36
62            android:theme="@style/LaunchTheme"
62-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:11:13-47
63            android:windowSoftInputMode="adjustResize" >
63-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:14:13-55
64
65            <!--
66                 Specifies an Android theme to apply to this Activity as soon as
67                 the Android process has started. This theme is visible to the user
68                 while the Flutter UI initializes. After that, this theme continues
69                 to determine the Window background behind the Flutter UI.
70            -->
71            <meta-data
71-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:19:13-22:17
72                android:name="io.flutter.embedding.android.NormalTheme"
72-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:20:15-70
73                android:resource="@style/NormalTheme" />
73-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:21:15-52
74
75            <intent-filter>
75-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:23:13-26:29
76                <action android:name="android.intent.action.MAIN" />
76-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:24:17-68
76-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:24:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:25:17-76
78-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:25:27-74
79            </intent-filter>
80        </activity>
81        <!--
82             Don't delete the meta-data below.
83             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
84        -->
85        <meta-data
85-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:30:9-32:33
86            android:name="flutterEmbedding"
86-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:31:13-44
87            android:value="2" />
87-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:32:13-30
88
89        <service
89-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:56
90            android:name="com.baseflow.geolocator.GeolocatorLocationService"
90-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-77
91            android:enabled="true"
91-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-35
92            android:exported="false"
92-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
93            android:foregroundServiceType="location" />
93-->[:geolocator_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\geolocator_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-53
94        <service
94-->[:firebase_auth] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
95            android:name="com.google.firebase.components.ComponentDiscoveryService"
95-->[:firebase_auth] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
96            android:directBootAware="true"
96-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:32:13-43
97            android:exported="false" >
97-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:31:13-37
98            <meta-data
98-->[:firebase_auth] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
99                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
99-->[:firebase_auth] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
100                android:value="com.google.firebase.components.ComponentRegistrar" />
100-->[:firebase_auth] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
101            <meta-data
101-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-38:85
102                android:name="com.google.firebase.components:io.flutter.plugins.firebase.messaging.FlutterFirebaseAppRegistrar"
102-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:37:17-128
103                android:value="com.google.firebase.components.ComponentRegistrar" />
103-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:17-82
104            <meta-data
104-->[:firebase_core] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
105                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
105-->[:firebase_core] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
106                android:value="com.google.firebase.components.ComponentRegistrar" />
106-->[:firebase_core] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
107            <meta-data
107-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:32:13-34:85
108                android:name="com.google.firebase.components:com.google.firebase.iid.Registrar"
108-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:33:17-96
109                android:value="com.google.firebase.components.ComponentRegistrar" />
109-->[com.google.firebase:firebase-iid:21.1.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\737192d229988eeb9d678082fa979864\transformed\jetified-firebase-iid-21.1.0\AndroidManifest.xml:34:17-82
110            <meta-data
110-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
111                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
111-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
112                android:value="com.google.firebase.components.ComponentRegistrar" />
112-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
113            <meta-data
113-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:57:13-59:85
114                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
114-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:58:17-122
115                android:value="com.google.firebase.components.ComponentRegistrar" />
115-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:59:17-82
116            <meta-data
116-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:60:13-62:85
117                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
117-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:61:17-119
118                android:value="com.google.firebase.components.ComponentRegistrar" />
118-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:62:17-82
119            <meta-data
119-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:15:13-17:85
120                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
120-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:16:17-130
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:17:17-82
122            <meta-data
122-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:18:13-20:85
123                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
123-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:19:17-127
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-installations:17.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\9ef3998b81af99eb96b69178f9d27b0d\transformed\jetified-firebase-installations-17.2.0\AndroidManifest.xml:20:17-82
125            <meta-data
125-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7872a1178712ba0f6ecf207676e97ecb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:12:13-14:85
126                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
126-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7872a1178712ba0f6ecf207676e97ecb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:13:17-116
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-common-ktx:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\7872a1178712ba0f6ecf207676e97ecb\transformed\jetified-firebase-common-ktx-20.4.3\AndroidManifest.xml:14:17-82
128            <meta-data
128-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:35:13-37:85
129                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
129-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:36:17-109
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:37:17-82
131            <meta-data
131-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\375af4550d6cebf4e0ac94123fb223dc\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:27:13-29:85
132                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
132-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\375af4550d6cebf4e0ac94123fb223dc\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:28:17-115
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-datatransport:18.1.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\375af4550d6cebf4e0ac94123fb223dc\transformed\jetified-firebase-datatransport-18.1.7\AndroidManifest.xml:29:17-82
134        </service>
135        <service
135-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-17:72
136            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingBackgroundService"
136-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-107
137            android:exported="false"
137-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
138            android:permission="android.permission.BIND_JOB_SERVICE" />
138-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-69
139        <service
139-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-24:19
140            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
140-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-97
141            android:exported="false" >
141-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-37
142            <intent-filter>
142-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
143                <action android:name="com.google.firebase.MESSAGING_EVENT" />
143-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
143-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
144            </intent-filter>
145        </service>
146
147        <receiver
147-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-33:20
148            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingReceiver"
148-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-98
149            android:exported="true"
149-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-36
150            android:permission="com.google.android.c2dm.permission.SEND" >
150-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-73
151            <intent-filter>
151-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
152                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
152-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
152-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
153            </intent-filter>
154        </receiver>
155
156        <provider
156-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:9-45:38
157            android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingInitProvider"
157-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-102
158            android:authorities="com.example.otlop_app.flutterfirebasemessaginginitprovider"
158-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-88
159            android:exported="false"
159-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:13-37
160            android:initOrder="99" />
160-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:13-35
161        <provider
161-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
162            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
162-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
163            android:authorities="com.example.otlop_app.flutter.image_provider"
163-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
164            android:exported="false"
164-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
165            android:grantUriPermissions="true" >
165-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
166            <meta-data
166-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
167                android:name="android.support.FILE_PROVIDER_PATHS"
167-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
168                android:resource="@xml/flutter_image_picker_file_paths" />
168-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
169        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
170        <service
170-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
171            android:name="com.google.android.gms.metadata.ModuleDependencies"
171-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
172            android:enabled="false"
172-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
173            android:exported="false" >
173-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
174            <intent-filter>
174-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
175                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
175-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
175-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
176            </intent-filter>
177
178            <meta-data
178-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
179                android:name="photopicker_activity:0:required"
179-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
180                android:value="" />
180-->[:image_picker_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
181        </service>
182
183        <activity
183-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
184            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
184-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
185            android:exported="false"
185-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
186            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
186-->[:url_launcher_android] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
187        <uses-library
187-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
188            android:name="org.apache.http.legacy"
188-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
189            android:required="false" />
189-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f104be3ac9e19655348c2861d0764aad\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
190
191        <activity
191-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
192            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
192-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
193            android:excludeFromRecents="true"
193-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
194            android:exported="true"
194-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
195            android:launchMode="singleTask"
195-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
196            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
196-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
197            <intent-filter>
197-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
198                <action android:name="android.intent.action.VIEW" />
198-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
198-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
199
200                <category android:name="android.intent.category.DEFAULT" />
200-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
200-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
201                <category android:name="android.intent.category.BROWSABLE" />
201-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
201-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
202
203                <data
203-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:42:13-50
204                    android:host="firebase.auth"
205                    android:path="/"
206                    android:scheme="genericidp" />
207            </intent-filter>
208        </activity>
209        <activity
209-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
210            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
210-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
211            android:excludeFromRecents="true"
211-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
212            android:exported="true"
212-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
213            android:launchMode="singleTask"
213-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
214            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
214-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
215            <intent-filter>
215-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
216                <action android:name="android.intent.action.VIEW" />
216-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
216-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
217
218                <category android:name="android.intent.category.DEFAULT" />
218-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
218-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
219                <category android:name="android.intent.category.BROWSABLE" />
219-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
219-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\1aebbadb8cfa120dfba1b908806ef748\transformed\jetified-firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
220
221                <data
221-->C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\android\app\src\main\AndroidManifest.xml:42:13-50
222                    android:host="firebase.auth"
223                    android:path="/"
224                    android:scheme="recaptcha" />
225            </intent-filter>
226        </activity>
227
228        <receiver
228-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:29:9-40:20
229            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
229-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:30:13-78
230            android:exported="true"
230-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:31:13-36
231            android:permission="com.google.android.c2dm.permission.SEND" >
231-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:32:13-73
232            <intent-filter>
232-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:13-32:29
233                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
233-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:17-81
233-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:25-78
234            </intent-filter>
235
236            <meta-data
236-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:37:13-39:40
237                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
237-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:38:17-92
238                android:value="true" />
238-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:39:17-37
239        </receiver>
240        <!--
241             FirebaseMessagingService performs security checks at runtime,
242             but set to not exported to explicitly avoid allowing another app to call it.
243        -->
244        <service
244-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:46:9-53:19
245            android:name="com.google.firebase.messaging.FirebaseMessagingService"
245-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:47:13-82
246            android:directBootAware="true"
246-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:48:13-43
247            android:exported="false" >
247-->[com.google.firebase:firebase-messaging:23.4.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\8c21d26b8afe4e661ebda28fc9159d92\transformed\jetified-firebase-messaging-23.4.1\AndroidManifest.xml:49:13-37
248            <intent-filter android:priority="-500" >
248-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-23:29
249                <action android:name="com.google.firebase.MESSAGING_EVENT" />
249-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:17-78
249-->[:firebase_messaging] C:\Users\<USER>\Desktop\Rest Delivery\otlop_app\build\firebase_messaging\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:25-75
250            </intent-filter>
251        </service>
252
253        <provider
253-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:23:9-28:39
254            android:name="com.google.firebase.provider.FirebaseInitProvider"
254-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:24:13-77
255            android:authorities="com.example.otlop_app.firebaseinitprovider"
255-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:25:13-72
256            android:directBootAware="true"
256-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:26:13-43
257            android:exported="false"
257-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:27:13-37
258            android:initOrder="100" />
258-->[com.google.firebase:firebase-common:20.4.3] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\67fdfc7e59ca55c3dc07e9e88ccc4671\transformed\jetified-firebase-common-20.4.3\AndroidManifest.xml:28:13-36
259
260        <activity
260-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
261            android:name="com.google.android.gms.common.api.GoogleApiActivity"
261-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
262            android:exported="false"
262-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
263            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
263-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\b93785a04fcd49acabbaa600426866db\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
264
265        <uses-library
265-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
266            android:name="androidx.window.extensions"
266-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
267            android:required="false" />
267-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
268        <uses-library
268-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
269            android:name="androidx.window.sidecar"
269-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
270            android:required="false" />
270-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\aed2861b2b6894f7067df3f3f96b791f\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
271
272        <meta-data
272-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
273            android:name="com.google.android.gms.version"
273-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
274            android:value="@integer/google_play_services_version" />
274-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\48bb13e58e49cdf1e6377472107b28a8\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
275
276        <provider
276-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
277            android:name="androidx.startup.InitializationProvider"
277-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
278            android:authorities="com.example.otlop_app.androidx-startup"
278-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
279            android:exported="false" >
279-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
280            <meta-data
280-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
281                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
281-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
282                android:value="androidx.startup" />
282-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\bb10008c4b33ee9597583412a41828ed\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
283            <meta-data
283-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
284                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
285                android:value="androidx.startup" />
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
286        </provider>
287
288        <receiver
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
289            android:name="androidx.profileinstaller.ProfileInstallReceiver"
289-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
290            android:directBootAware="false"
290-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
291            android:enabled="true"
291-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
292            android:exported="true"
292-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
293            android:permission="android.permission.DUMP" >
293-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
294            <intent-filter>
294-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
295                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
295-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
296            </intent-filter>
297            <intent-filter>
297-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
298                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
298-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
299            </intent-filter>
300            <intent-filter>
300-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
301                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
301-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
302            </intent-filter>
303            <intent-filter>
303-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
304                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
304-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\5aacd87baa6feaa7bece87b01e7698f0\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
305            </intent-filter>
306        </receiver>
307
308        <service
308-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
309            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
309-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
310            android:exported="false" >
310-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
311            <meta-data
311-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
312                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
312-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
313                android:value="cct" />
313-->[com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\e22bb42c45fed902b8e1f60c832f152f\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
314        </service>
315        <service
315-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
316            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
316-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
317            android:exported="false"
317-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
318            android:permission="android.permission.BIND_JOB_SERVICE" >
318-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
319        </service>
320
321        <receiver
321-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
322            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
322-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
323            android:exported="false" />
323-->[com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\a5c420f2511f6b65d0dcf2bcb1084ef7\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
324    </application>
325
326</manifest>
