import 'package:otlop_app/services/api_service.dart';
import 'package:otlop_app/providers/auth_provider.dart';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';

class OrderService {
  final ApiService _apiService;

  // Constructor
  OrderService({ApiService? apiService}) : _apiService = apiService ?? ApiService();

  // Get customer orders
  Future<List<dynamic>> getCustomerOrders({BuildContext? context}) async {
    try {
      // Get customer ID from auth provider if context is provided
      String? customerId;
      if (context != null) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        customerId = authProvider.user?.id;
      }

      String endpoint = 'orders/all';
      if (customerId != null) {
        endpoint = 'orders/customer/$customerId';
      }

      final response = await _apiService.get(endpoint);
      return response['items'] ?? [];
    } catch (e) {
      throw Exception('Failed to load orders: ${e.toString()}');
    }
  }

  // Get order details by ID
  Future<Map<String, dynamic>> getOrderById(String orderId) async {
    try {
      final response = await _apiService.get('orders/details/$orderId');
      if (response is Map<String, dynamic>) {
        return response;
      }
      return {};
    } catch (e) {
      throw Exception('Failed to load order details: ${e.toString()}');
    }
  }

  // Get order cart details
  Future<Map<String, dynamic>> getOrderCart(String orderId) async {
    try {
      final response = await _apiService.get('orders/cart/$orderId');
      if (response is Map<String, dynamic>) {
        return response;
      }
      return {};
    } catch (e) {
      throw Exception('Failed to load order cart: ${e.toString()}');
    }
  }

  // Create a new order
  Future<String> createOrder(Map<String, dynamic> orderData) async {
    try {
      print('Creating order with data: ${orderData.toString()}');
      final response = await _apiService.post('orders/create', data: orderData);

      // Handle different response formats
      if (response is Map<String, dynamic>) {
        if (response['success'] == true) {
          final orderId = response['OrderID']?.toString() ??
                         response['data']?['orderId']?.toString() ?? '';
          print('Order created successfully with ID: $orderId');
          return orderId;
        } else {
          final errorMessage = response['message'] ?? 'Unknown error occurred';
          throw Exception('Order creation failed: $errorMessage');
        }
      }

      // Fallback for direct OrderID response
      return response['OrderID']?.toString() ?? '';
    } catch (e) {
      print('Error creating order: ${e.toString()}');
      if (e.toString().contains('400')) {
        throw Exception('Invalid order data. Please check your cart and try again.');
      } else if (e.toString().contains('500')) {
        throw Exception('Server error. Please try again later.');
      }
      throw Exception('Failed to create order: ${e.toString()}');
    }
  }

  // Cancel an order
  Future<void> cancelOrder(String orderId) async {
    try {
      // Update order status to cancelled (6)
      await _apiService.put('orders/$orderId', {
        'Status': 6
      });
    } catch (e) {
      throw Exception('Failed to cancel order: ${e.toString()}');
    }
  }

  // Add item to cart
  Future<Map<String, dynamic>> addToCart(Map<String, dynamic> cartItem) async {
    try {
      final response = await _apiService.post('cart', data: cartItem);
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to add item to cart: ${e.toString()}');
    }
  }

  // Get cart items
  Future<List<dynamic>> getCartItems() async {
    try {
      final response = await _apiService.get('cart');
      return response['items'] ?? [];
    } catch (e) {
      throw Exception('Failed to fetch cart items: ${e.toString()}');
    }
  }

  // Update cart item quantity
  Future<Map<String, dynamic>> updateCartItemQuantity(String cartItemId, int quantity) async {
    try {
      final response = await _apiService.put('cart/$cartItemId', {
        'Quantity': quantity,
      });
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to update cart item: ${e.toString()}');
    }
  }

  // Remove cart item
  Future<void> removeCartItem(String cartItemId) async {
    try {
      await _apiService.delete('cart/$cartItemId');
    } catch (e) {
      throw Exception('Failed to remove cart item: ${e.toString()}');
    }
  }

  // Checkout from cart
  Future<Map<String, dynamic>> checkout(Map<String, dynamic> checkoutData) async {
    try {
      final response = await _apiService.post('checkout', data: checkoutData);
      return response as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to checkout: ${e.toString()}');
    }
  }

  // Get restaurant orders
  Future<List<dynamic>> getRestaurantOrders(String restaurantId) async {
    try {
      final response = await _apiService.get('orders/restaurant/$restaurantId');
      if (response is Map<String, dynamic> && response.containsKey('items')) {
        return response['items'] as List<dynamic>;
      } else if (response is List<dynamic>) {
        return response as List<dynamic>;
      }
      return [];
    } catch (e) {
      throw Exception('Failed to load restaurant orders: ${e.toString()}');
    }
  }

  // Get driver orders
  Future<List<dynamic>> getDriverOrders(String driverId) async {
    try {
      final response = await _apiService.get('orders/driver/$driverId');
      if (response is Map<String, dynamic> && response.containsKey('items')) {
        return response['items'] as List<dynamic>;
      } else if (response is List<dynamic>) {
        return response as List<dynamic>;
      }
      return [];
    } catch (e) {
      throw Exception('Failed to load driver orders: ${e.toString()}');
    }
  }
}