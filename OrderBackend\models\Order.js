const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Order', {
    OrderID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    DriverID: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Driver',
        key: 'DriverID'
      }
    },
    CartID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Cart',
        key: 'CartID'
      }
    },
    CustomerID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Customer',
        key: 'CustomerID'
      }
    },
    StartPointLongitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    StartPointLatitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    EndPointLongitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    EndPointLatitude: {
      type: DataTypes.DECIMAL(9,6),
      allowNull: true
    },
    Duration: {
      type: DataTypes.TIME,
      allowNull: true
    },
    OrderDate: {
      type: DataTypes.DATE,
      allowNull: false
    },
    Status: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    Note: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    Address: {
      type: DataTypes.STRING(500),
      allowNull: true
    },
    PaymentMethod: {
      type: DataTypes.STRING(50),
      allowNull: true,
      defaultValue: 'cash'
    }
  }, {
    sequelize,
    tableName: 'Order',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Order__C3905BAF88A77E1C",
        unique: true,
        fields: [
          { name: "OrderID" },
        ]
      },
    ]
  });
};
