import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/models/order_model.dart' as order_model;
import 'package:otlop_app/models/product_model.dart';
import 'package:otlop_app/services/order_service.dart';
import 'package:otlop_app/services/cart_storage_service.dart';
import 'package:otlop_app/providers/auth_provider.dart';

class OrderProvider with ChangeNotifier {
  final OrderService _orderService;
  final CartStorageService _cartStorageService = CartStorageService();

  List<order_model.Order> _orders = [];
  order_model.Order? _selectedOrder;
  List<CartItem> _cartItems = [];
  bool _isLoading = false;
  String? _error;

  OrderProvider({required OrderService orderService}) : _orderService = orderService {
    // Initialize cart storage and load cart items
    _initCartStorage();
  }

  // Initialize cart storage
  Future<void> _initCartStorage() async {
    debugPrint('DEBUG: Initializing cart storage');
    await _cartStorageService.init();
    debugPrint('DEBUG: Cart storage initialized');
    await _loadCartItemsFromStorage();
  }

  // Load cart items from storage
  Future<void> _loadCartItemsFromStorage() async {
    try {
      debugPrint('DEBUG: Loading cart items from storage');
      _cartItems = await _cartStorageService.getCartItems();
      debugPrint('DEBUG: Loaded ${_cartItems.length} cart items from storage');
      notifyListeners();
    } catch (e) {
      debugPrint('ERROR: Failed to load cart items: ${e.toString()}');
      _setError('Failed to load cart items: ${e.toString()}');
    }
  }

  // Getters
  List<order_model.Order> get orders => _orders;
  order_model.Order? get selectedOrder => _selectedOrder;
  List<CartItem> get cartItems => _cartItems;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Calculate cart total
  double get cartTotal => _cartItems.fold(
        0,
        (total, item) => total + (item.price * item.quantity),
      );

  // Get a specific order by ID
  Future<order_model.Order> getOrderDetails(String orderId) async {
    _setLoading(true);
    try {
      final orderData = await _orderService.getOrderById(orderId);
      final order = order_model.Order.fromJson(orderData);
      _setLoading(false);
      return order;
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      rethrow;
    }
  }

  // Load user orders
  Future<void> loadUserOrders({BuildContext? context}) async {
    _setLoading(true);
    try {
      final ordersData = await _orderService.getCustomerOrders(context: context);
      _orders = ordersData.map((orderJson) => order_model.Order.fromJson(orderJson)).toList();
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
    }
  }

  // Place a new order
  Future<String?> placeOrder(Map<String, dynamic> orderData, {BuildContext? context}) async {
    // Clear any previous errors
    _error = null;

    if (_cartItems.isEmpty) {
      _setError('Your cart is empty');
      return null;
    }

    _setLoading(true);
    try {
      // Get customer ID from auth provider if context is provided
      String? customerId;
      if (context != null) {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        customerId = authProvider.user?.id;
      }

      // Validate cart items are from the same restaurant
      if (_cartItems.isNotEmpty) {
        final firstRestaurantId = _cartItems.first.restaurantId;
        final allSameRestaurant = _cartItems.every((item) => item.restaurantId == firstRestaurantId);
        if (!allSameRestaurant) {
          _setError('All items must be from the same restaurant');
          _setLoading(false);
          return null;
        }
      }

      // Prepare the order data with cart items and customer ID
      final orderWithItems = {
        ...orderData,
        'customerId': customerId != null ? int.tryParse(customerId) : null,
        'items': _cartItems.map((item) => {
          'productId': item.productId,
          'productName': item.productName,
          'price': item.price,
          'quantity': item.quantity,
          'restaurantId': item.restaurantId,
          'restaurantName': item.restaurantName,
          'selectedIngredients': item.selectedIngredients?.map((ing) => {
            'id': ing.ingredientId.toString(),
            'name': ing.name,
            'price': ing.price ?? 0.0,
            'quantity': 1,
          }).toList() ?? [],
        }).toList(),
      };

      // Validate required fields
      if (orderWithItems['customerId'] == null) {
        print('ERROR: Customer ID is null');
        _setError('Customer ID is required');
        _setLoading(false);
        return null;
      }

      if (orderWithItems['items'] == null || (orderWithItems['items'] as List).isEmpty) {
        print('ERROR: Cart is empty');
        _setError('Cart is empty');
        _setLoading(false);
        return null;
      }

      print('Calling order service with data: ${orderWithItems.toString()}');

      // Call API to create the order
      final orderId = await _orderService.createOrder(orderWithItems);

      print('Order service returned: $orderId');

      // Clear the cart after successful order
      await _cartStorageService.clearCart();
      _cartItems = [];

      // Refresh the orders list
      await loadUserOrders();

      _setLoading(false);
      notifyListeners();
      return orderId;
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
      return null;
    }
  }

  // Add item to cart (local storage version)
  Future<void> addItemToCart(Product product, int quantity, List<SelectedIngredient>? selectedIngredients, String? note) async {
      debugPrint('DEBUG: - - - - - product to cart: ${product.restaurantName} - ${product.name}');
    try {
      _setLoading(true);

      // Add to local storage
      await _cartStorageService.addToCart(product, quantity, selectedIngredients);

      // Reload cart items from storage
      await _loadCartItemsFromStorage();

      _setLoading(false);
    } catch (e) {
      _setLoading(false);
      _setError(e.toString());
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String? errorMessage) {
    _error = errorMessage;
    notifyListeners();
  }

  // Fetch customer orders
  Future<void> fetchOrders({BuildContext? context}) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final ordersData = await _orderService.getCustomerOrders(context: context);
      _orders = ordersData.map((orderJson) => order_model.Order.fromJson(orderJson)).toList();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch order details
  Future<void> fetchOrderById(String orderId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final orderData = await _orderService.getOrderById(orderId);
      _selectedOrder = order_model.Order.fromJson(orderData);
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Fetch cart items from local storage
  Future<void> fetchCartItems() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Load from local storage
      await _loadCartItemsFromStorage();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Add item to cart (API version - now uses local storage)
  Future<void> addToCartApi(Map<String, dynamic> cartItemData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Extract product data from cart item
      debugPrint('DEBUG: - - - - Restaurant ID ${cartItemData['restaurantName']} Adding to cart: ${cartItemData['productName']}');
      final int productId = cartItemData['productId'];
      final int quantity = cartItemData['quantity'];
      final int restaurantId = cartItemData['restaurantId'] ?? 0;
      final List<dynamic> selectedIngredientsData = cartItemData['selectedIngredients'] ?? [];

      // Create product from cart item data
      final String productName = cartItemData['productName'] ?? 'Product $productId';
      final double productPrice = cartItemData['price'] != null
          ? (cartItemData['price'] is double
              ? cartItemData['price']
              : double.tryParse(cartItemData['price'].toString()) ?? 0.0)
          : 0.0;
      final String? productImage = cartItemData['productImage'];
      final String restaurantName = cartItemData['restaurantName'] ?? 'Restaurant';

      final product = Product(
        id: productId,
        name: productName,
        price: productPrice,
        image: productImage,
        available: true,
        restaurantId: restaurantId,
        restaurantName: restaurantName,
      );

      // Convert selected ingredients
      List<SelectedIngredient>? selectedIngredients;
      if (selectedIngredientsData.isNotEmpty) {
        selectedIngredients = selectedIngredientsData.map((item) {
          return SelectedIngredient(
            id: 0, // Temporary ID
            ingredientId: item['ingredientId'],
            name: item['name'],
            price: item['price']?.toDouble(),
          );
        }).toList();
      }

      // Check if product is already in cart
      final isInCart = await isProductInCart(productId);
      if (isInCart) {
        _error = 'This product is already in your cart';
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Add to local storage
      await _cartStorageService.addToCart(product, quantity, selectedIngredients);

      // Reload cart items
      await _loadCartItemsFromStorage();
    } catch (e) {
      _error = e.toString();
      // Rethrow the exception so it can be caught by the UI
      rethrow;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Update cart item quantity
  Future<void> updateCartItemQuantity(String cartItemId, int quantity) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Convert string ID to int
      final int id = int.parse(cartItemId);

      // Update in local storage
      await _cartStorageService.updateCartItemQuantity(id, quantity);

      // Reload cart items
      await _loadCartItemsFromStorage();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Remove cart item
  Future<void> removeCartItem(String cartItemId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Convert string ID to int
      final int id = int.parse(cartItemId);

      // Remove from local storage
      await _cartStorageService.removeCartItem(id);

      // Reload cart items
      await _loadCartItemsFromStorage();
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear cart
  Future<void> clearCart() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      // Clear local storage cart
      await _cartStorageService.clearCart();

      // Clear in-memory cart
      _cartItems = [];
    } catch (e) {
      _error = e.toString();
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Checkout
  Future<order_model.Order?> checkout(Map<String, dynamic> checkoutData) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final orderData = await _orderService.checkout(checkoutData);
      final newOrder = order_model.Order.fromJson(orderData);

      // Add to orders list and clear cart (both in memory and storage)
      _orders = [newOrder, ..._orders];
      await _cartStorageService.clearCart();
      _cartItems = [];

      return newOrder;
    } catch (e) {
      _error = e.toString();
      return null;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Cancel order
  Future<bool> cancelOrder(String orderId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      await _orderService.cancelOrder(orderId);

      // Update local order status
      final index = _orders.indexWhere((order) => order.id.toString() == orderId);
      if (index != -1) {
        // Create a new order with cancelled status (6)
        // Note: This is a simplification. In reality, the backend should return the updated order
        final updatedOrder = order_model.Order(
          id: _orders[index].id,
          items: _orders[index].items,
          status: 6, // Cancelled status
          total: _orders[index].total,
          address: _orders[index].address,
          paymentMethod: _orders[index].paymentMethod,
          orderDate: _orders[index].orderDate,
          restaurant: _orders[index].restaurant,
          cartId: _orders[index].cartId,
          customerId: _orders[index].customerId,
          driverId: _orders[index].driverId,
          note: _orders[index].note,
          restaurantName: _orders[index].restaurantName,
          driverFirstName: _orders[index].driverFirstName,
          driverLastName: _orders[index].driverLastName,
          driverPhone: _orders[index].driverPhone,
        );

        _orders = List.from(_orders)
          ..removeAt(index)
          ..insert(index, updatedOrder);
      }

      return true;
    } catch (e) {
      _error = e.toString();
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Check if a product is already in the cart
  Future<bool> isProductInCart(int productId) async {
    return await _cartStorageService.isProductInCart(productId);
  }

  // Check if a product is from a different restaurant than what's in the cart
  Future<bool> isFromDifferentRestaurant(int restaurantId) async {
    return await _cartStorageService.isFromDifferentRestaurant(restaurantId);
  }

  // Get the current restaurant in cart (if any)
  Future<Map<String, dynamic>?> getCurrentRestaurantInCart() async {
    return await _cartStorageService.getCurrentRestaurantInCart();
  }
}