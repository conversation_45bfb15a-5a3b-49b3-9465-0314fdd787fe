const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('Ingredient', {
    IngredientID: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    IngredientName: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    ProductID: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Product',
        key: 'ProductID'
      }
    }
  }, {
    sequelize,
    tableName: 'Ingredient',
    schema: 'dbo',
    timestamps: false,
    indexes: [
      {
        name: "PK__Ingredie__BEAEB27AD479B23D",
        unique: true,
        fields: [
          { name: "IngredientID" },
        ]
      },
    ]
  });
};
