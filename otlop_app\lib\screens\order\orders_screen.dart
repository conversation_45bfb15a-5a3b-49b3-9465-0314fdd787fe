import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:otlop_app/models/order_model.dart' as order_model;
import 'package:otlop_app/providers/order_provider.dart';
import 'package:otlop_app/screens/order/order_tracking_screen.dart';
import 'package:intl/intl.dart';

class OrdersScreen extends StatefulWidget {
  const OrdersScreen({Key? key}) : super(key: key);

  @override
  _OrdersScreenState createState() => _OrdersScreenState();
}

class _OrdersScreenState extends State<OrdersScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      await orderProvider.loadUserOrders();
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load orders: ${e.toString()}';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'My Orders',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontFamily: 'Alx',
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Theme.of(context).colorScheme.primary,
          tabs: const [
            Tab(text: 'Active'),
            Tab(text: 'Completed'),
            Tab(text: 'Cancelled'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadOrders,
            tooltip: 'Refresh Orders',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.error_outline, size: 48, color: Colors.red),
                      const SizedBox(height: 16),
                      Text(
                        'Error loading orders',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _errorMessage!,
                        style: TextStyle(
                          color: Colors.grey[600],
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 24),
                      ElevatedButton(
                        onPressed: _loadOrders,
                        child: const Text('Try Again'),
                      ),
                    ],
                  ),
                )
              : Consumer<OrderProvider>(
                  builder: (context, orderProvider, child) {
                    final orders = orderProvider.orders;

                    if (orders.isEmpty) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.receipt_long,
                              size: 80,
                              color: Colors.grey[400],
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No orders yet',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Your order history will appear here',
                              style: TextStyle(
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 24),
                            ElevatedButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('Explore Restaurants'),
                            ),
                          ],
                        ),
                      );
                    }

                    // Filter orders based on tab
                    final List<order_model.Order> activeOrders = [];
                    final List<order_model.Order> completedOrders = [];
                    final List<order_model.Order> cancelledOrders = [];

                    for (final order in orders) {
                      if (order.status == 6) { // 6 = cancelled
                        cancelledOrders.add(order);
                      } else if (order.status == 5) { // 5 = delivered
                        completedOrders.add(order);
                      } else {
                        activeOrders.add(order);
                      }
                    }

                    return TabBarView(
                      controller: _tabController,
                      children: [
                        // Active orders tab
                        _buildOrderList(activeOrders, orderProvider),

                        // Completed orders tab
                        _buildOrderList(completedOrders, orderProvider),

                        // Cancelled orders tab
                        _buildOrderList(cancelledOrders, orderProvider),
                      ],
                    );
                  },
                ),
    );
  }

  Widget _buildOrderList(List<order_model.Order> orders, OrderProvider orderProvider) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.receipt_long,
              size: 60,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No orders in this category',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderCard(order, context);
      },
    );
  }

  Widget _buildOrderCard(order_model.Order order, BuildContext context) {
    final createdAt = DateFormat('MMM dd, yyyy · hh:mm a').format(order.orderDate);

    // Map status to a more readable format
    final Map<int, String> statusText = {
      0: 'Order Received',
      1: 'Order Confirmed',
      2: 'Preparing',
      3: 'Ready for Pickup',
      4: 'Out for Delivery',
      5: 'Delivered',
      6: 'Cancelled',
    };

    // Map status to appropriate colors
    final Map<int, Color> statusColors = {
      0: Colors.blue,
      1: Colors.blue[700]!,
      2: Colors.orange,
      3: Colors.amber,
      4: Colors.green,
      5: Colors.green[700]!,
      6: Colors.red,
    };

    final status = statusText[order.status ?? 0] ?? 'Status: ${order.status}';
    final statusColor = statusColors[order.status ?? 0] ?? Colors.grey;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order status header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    status,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: statusColor,
                    ),
                  ),
                ),
                Text(
                  '#${order.id}',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          // Order details
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Restaurant info
                Row(
                  children: [
                    CircleAvatar(
                      backgroundColor: Colors.grey[200],
                      radius: 20,
                      backgroundImage: order.restaurant?.image != null && order.restaurant!.image!.isNotEmpty
                          ? NetworkImage(order.restaurant!.image!)
                          : null,
                      child: order.restaurant?.image == null || (order.restaurant != null && order.restaurant!.image!.isEmpty)
                          ? const Icon(Icons.restaurant, color: Colors.grey)
                          : null,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            order.restaurant?.name ?? order.restaurantName ?? 'Unknown Restaurant',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Text(
                            createdAt,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '\$${order.total.toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Order items summary
                Text(
                  'Items: ${_getOrderItemSummary(order)}',
                  style: TextStyle(
                    color: Colors.grey[700],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 16),
                const Divider(height: 1),
                const SizedBox(height: 16),

                // Action buttons
                Row(
                  mainAxisAlignment: order.status == 5 || order.status == 6 // 5=delivered, 6=cancelled
                      ? MainAxisAlignment.center
                      : MainAxisAlignment.spaceEvenly,
                  children: [
                    // For completed orders, show "Reorder" button
                    if (order.status == 5) // 5=delivered
                      ElevatedButton.icon(
                        icon: const Icon(Icons.replay),
                        label: const Text('Reorder'),
                        onPressed: () {
                          // TODO: Implement reorder functionality
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),

                    // For active orders, show "Track Order" button
                    if (order.status != 5 && order.status != 6) // not delivered or cancelled
                      ElevatedButton.icon(
                        icon: const Icon(Icons.delivery_dining),
                        label: const Text('Track Order'),
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => OrderTrackingScreen(orderId: order.id.toString()),
                            ),
                          );
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Theme.of(context).colorScheme.primary,
                          foregroundColor: Colors.white,
                        ),
                      ),

                    // For active orders, show "Cancel" button (except if out for delivery)
                    if (order.status != 5 && // not delivered
                        order.status != 6 && // not cancelled
                        order.status != 4)   // not out for delivery
                      OutlinedButton.icon(
                        icon: const Icon(Icons.cancel_outlined),
                        label: const Text('Cancel'),
                        onPressed: () {
                          _showCancelConfirmation(context, order.id.toString());
                        },
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getOrderItemSummary(order_model.Order order) {
    if (order.items.isEmpty) return 'No items';

    final buffer = StringBuffer();

    for (var i = 0; i < order.items.length; i++) {
      if (i > 0) buffer.write(', ');
      buffer.write('${order.items[i].quantity}x ${order.items[i].productName}');

      // Limit the summary length
      if (i == 1 && order.items.length > 2) {
        buffer.write(', +${order.items.length - 2} more');
        break;
      }
    }

    return buffer.toString();
  }

  void _showCancelConfirmation(BuildContext context, String orderId) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Cancel Order'),
          content: const Text('Are you sure you want to cancel this order?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop();

                setState(() {
                  _isLoading = true;
                });

                try {
                  final orderProvider = Provider.of<OrderProvider>(context, listen: false);
                  await orderProvider.cancelOrder(orderId);

                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Order cancelled successfully')),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('Failed to cancel order: ${e.toString()}')),
                    );
                  }
                } finally {
                  if (mounted) {
                    setState(() {
                      _isLoading = false;
                    });
                  }
                }
              },
              child: const Text(
                'Yes, Cancel',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }
}